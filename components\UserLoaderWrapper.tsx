"use client";

import { useUser } from "@/lib/contexts/UserContext";
import { useEffect, useRef, useState } from "react";
import { StylesProvider } from "@/lib/contexts/StylesContext";
import { GenerationProvider } from "@/lib/contexts/GenerationContext";
import { Navbar } from "@/components/layout/navbar";
import { PageViewTracker } from "@/components/analytics/PageViewTracker";
import dynamic from "next/dynamic";
import { MobileBottomNav } from "@/components/layout/mobile-bottom-nav";
import React, { Suspense } from 'react';
import { ServiceWorkerRegistration } from "@/components/ServiceWorkerRegistration";
import UnifiedLoader from "@/components/ui/unified-loader";
import { AuthForm } from "@/components/auth/auth-form";
import AppBackground from "@/components/layout/BackgroundWrapper";

// NOTE: UserLoaderWrapper uses useUser and should only be used in protected layouts/routes.
// Do not use in public pages or root layout.
function UserLoaderWrapper({ children }: { children: React.ReactNode }) {
  const { isLoading, user } = useUser();
  const [showOverlay, setShowOverlay] = useState(true);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [hasMounted, setHasMounted] = useState(false);
  const appRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  useEffect(() => {
    if (!isLoading && user && showOverlay) {
      // Fade out overlay
      const overlayTimeout = setTimeout(() => {
        setShowOverlay(false);
      }, 400); // match overlay fade duration
      return () => clearTimeout(overlayTimeout);
    }
    if (isLoading) {
      setShowOverlay(true);
      setHasAnimatedIn(false);
    }
  }, [isLoading, user, showOverlay]);

  // Always trigger animation after overlay is gone and app is ready
  useEffect(() => {
    if (!showOverlay && !hasAnimatedIn && !isLoading && user) {
      setTimeout(() => {
        if (appRef.current) void appRef.current.offsetHeight; // force reflow
        setHasAnimatedIn(true);
      }, 0);
    }
  }, [showOverlay, hasAnimatedIn, isLoading, user]);

  const baseClass = "relative min-h-screen w-full flex font-sans antialiased z-0 opacity-0 pointer-events-none";
  const animatedClass = "relative min-h-screen w-full flex font-sans antialiased z-0 opacity-0 app-entrance-animate transition-opacity duration-500";

  // Spinner should show as soon as showOverlay is true
  const showSpinner = showOverlay;

  const DynamicGlobalGeneratedImageModal = dynamic(() => import("@/components/GlobalGeneratedImageModal").then(mod => mod.GlobalGeneratedImageModal), { ssr: false, loading: () => null });

  return (
    <>
      {/* Loader overlay with fade-out, only on first load */}
      <div
        className={`fixed inset-0 z-[9999] w-full h-full transition-opacity duration-400 ${showOverlay ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}`}
      >
        <AppBackground />
        {showSpinner && (
          <div className="absolute inset-0 flex items-center justify-center">
            <UnifiedLoader text="" />
          </div>
        )}
      </div>
      {/* Global overlays (Toaster) always rendered and not affected by app content transitions */}
      {/* <div id="global-overlays" className="fixed inset-0 pointer-events-none z-50">
        <Toaster
          theme="dark"
          position="top-right"
          toastOptions={{
            style: {
              background: "rgba(15, 23, 42, 0.9)",
              border: "1px solid rgba(139, 92, 246, 0.2)",
              color: "#f1f5f9",
            },
          }}
        />
      </div> */}
      {/* App content always rendered, always with id and className */}
      <div
        id="app-root"
        ref={appRef}
        className={hasAnimatedIn ? animatedClass : baseClass}
      >
        <StylesProvider>
          <GenerationProvider>
            <Navbar />
            <main
              className="flex-1 flex flex-col min-h-0 w-full overflow-hidden pb-20 md:pb-0"
            >
              <PageViewTracker>
                {children}
              </PageViewTracker>
            </main>
            <DynamicGlobalGeneratedImageModal />
            <ServiceWorkerRegistration />
          </GenerationProvider>
        </StylesProvider>
      </div>
      {/* Mobile navigation positioned outside the main layout container for proper fixed positioning */}
      {hasMounted && <MobileBottomNav />}
    </>
  );
}

export default UserLoaderWrapper; 