import { Image } from "lucide-react";

export function GalleryHeader({
  imagesCount,
  sharedCount,
}: {
  imagesCount: number;
  sharedCount: number;
}) {
  return (
    <div className="mb-2 page-transition">
      <div className="flex items-center gap-2 sm:gap-3">
        {/* Gallery Icon */}
        <div className="w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center">
          <Image className="h-5 w-5 sm:h-6 sm:w-6 text-blue-400" />
        </div>
        
        {/* Title and Count */}
        <div className="flex flex-col">
          <h1 className="text-2xl sm:text-3xl font-bold gradient-text leading-tight">
            My Gallery
          </h1>
          <p className="text-gray-400 text-xs sm:text-sm">
            {imagesCount} images • {sharedCount} shared
          </p>
        </div>
      </div>
    </div>
  );
}
