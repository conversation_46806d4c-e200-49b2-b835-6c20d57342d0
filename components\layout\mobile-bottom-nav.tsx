"use client";

import React, { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  Palette,
  Image,
  Users,
  User
} from "lucide-react";
import { useUser } from "@/lib/contexts/UserContext";

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
}

const navItems: NavItem[] = [
  {
    path: "/studio",
    label: "Studio",
    icon: Palette,
    gradient: "from-violet-500 to-purple-500"
  },
  {
    path: "/gallery",
    label: "Gallery",
    icon: Image,
    gradient: "from-violet-500 to-blue-500"
  },
  {
    path: "/community",
    label: "Community",
    icon: Users,
    gradient: "from-emerald-500 to-teal-500"
  },
  {
    path: "/profile",
    label: "Profile",
    icon: User,
    gradient: "from-violet-500 to-blue-500"
  }
];

// NOTE: MobileBottomNav uses useUser and should only be used in protected layouts/routes.
// If you need a public mobile nav, create a separate PublicMobileBottomNav component without useUser.
export function MobileBottomNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, isLoading } = useUser();
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Hide navigation if user is not logged in or component has not mounted
  if (!hasMounted || isLoading || !user) return null;



  return (
    <nav
      role="navigation"
      aria-label="Main mobile navigation"
      className="md:hidden fixed bottom-0 left-0 right-0 z-50"
    >
      <div className="glass-card border-t border-white/10 backdrop-blur-xl bg-slate-900/95 pb-safe rounded-none">
        <div className="flex items-center justify-between w-full">
          {navItems.map(({ icon: Icon, path, gradient }) => {
            const isActive = pathname === path;
            
            return (
              <button
                key={path}
                onClick={() => router.push(path)}
                aria-current={isActive ? "page" : undefined}
                className="flex-1 flex justify-center items-center p-2"
                style={{
                  border: 'none',
                  background: 'transparent',
                  outline: 'none',
                  boxShadow: 'none'
                }}
              >
                <div className={`p-2.5 rounded-xl ${
                  isActive 
                    ? `bg-gradient-to-r ${gradient} shadow-lg shadow-purple-500/25`
                    : 'bg-slate-700/80 border border-slate-600/50'
                }`}>
                  <Icon className="h-5 w-5 text-white" />
                </div>
              </button>
            );
          })}
        </div>
      </div>
    </nav>
  );
} 
